module github.com/paradoxe35/torra/packages/http

go 1.25

require (
	github.com/go-chi/chi/v5 v5.2.3
	github.com/google/uuid v1.6.0
	github.com/paradoxe35/torra/packages/i18n v0.0.0
	github.com/paradoxe35/torra/packages/logger v0.0.0
)

require (
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/stretchr/testify v1.11.1 // indirect
	golang.org/x/text v0.28.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

replace (
	github.com/paradoxe35/torra/packages/i18n => ../i18n
	github.com/paradoxe35/torra/packages/logger => ../logger
)

-- Create subscription_plans table for defining available subscription tiers
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL, -- e.g., "Basic", "Professional", "Premium"
    tier VARCHAR(20) NOT NULL, -- e.g., "basic", "professional", "premium"
    entity_type VARCHAR(50) NOT NULL, -- e.g., "seller", "restaurant", "driver"
    
    -- Pricing
    monthly_price DECIMAL(10, 2) NOT NULL,
    annual_price DECIMAL(10, 2), -- Optional annual pricing
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Trial configuration
    trial_days INT DEFAULT 0, -- Number of free trial days
    
    -- Features and limits
    features JSONB NOT NULL DEFAULT '{}', -- {"listings": 100, "photos_per_listing": 10, "featured_slots": 5}
    usage_limits JSONB NOT NULL DEFAULT '{}', -- {"api_calls": 1000, "storage_gb": 10}
    
    -- Plan configuration
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_default BOOLEAN NOT NULL DEFAULT false, -- Default plan for new subscriptions
    sort_order INT NOT NULL DEFAULT 0, -- For display ordering
    
    -- Metadata
    description TEXT,
    features_description JSONB, -- Detailed feature descriptions for UI
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    CONSTRAINT valid_tier CHECK (tier IN ('basic', 'professional', 'premium', 'enterprise')),
    CONSTRAINT valid_entity_type CHECK (entity_type IN ('seller', 'restaurant', 'driver', 'hotel', 'general')),
    CONSTRAINT positive_prices CHECK (monthly_price >= 0 AND (annual_price IS NULL OR annual_price >= 0)),
    CONSTRAINT valid_trial_days CHECK (trial_days >= 0)
);

-- Create unique constraint to ensure only one default plan per entity type
CREATE UNIQUE INDEX idx_subscription_plans_default_entity 
ON subscription_plans (entity_type) 
WHERE is_default = true;

-- Create indexes for common queries
CREATE INDEX idx_subscription_plans_entity_type ON subscription_plans(entity_type);
CREATE INDEX idx_subscription_plans_tier ON subscription_plans(tier);
CREATE INDEX idx_subscription_plans_active ON subscription_plans(is_active);
CREATE INDEX idx_subscription_plans_sort_order ON subscription_plans(sort_order);

-- Insert default subscription plans
INSERT INTO subscription_plans (
    name, tier, entity_type, monthly_price, annual_price, 
    trial_days, features, usage_limits, is_default, sort_order,
    description, features_description
) VALUES 
-- Seller plans
(
    'Basic Seller', 'basic', 'seller', 9.99, 99.99, 14,
    '{"listings": 50, "photos_per_listing": 5, "featured_slots": 1, "analytics": false, "priority_support": false}',
    '{"api_calls": 1000, "storage_gb": 5}',
    true, 1,
    'Perfect for individual sellers starting their business',
    '{"listings": "Up to 50 active listings", "photos_per_listing": "5 photos per listing", "featured_slots": "1 featured listing slot"}'
),
(
    'Professional Seller', 'professional', 'seller', 29.99, 299.99, 14,
    '{"listings": 200, "photos_per_listing": 10, "featured_slots": 5, "analytics": true, "priority_support": true, "bulk_upload": true}',
    '{"api_calls": 5000, "storage_gb": 20}',
    false, 2,
    'Ideal for growing businesses with advanced features',
    '{"listings": "Up to 200 active listings", "photos_per_listing": "10 photos per listing", "featured_slots": "5 featured listing slots", "analytics": "Advanced analytics dashboard"}'
),
(
    'Premium Seller', 'premium', 'seller', 59.99, 599.99, 14,
    '{"listings": -1, "photos_per_listing": 20, "featured_slots": 15, "analytics": true, "priority_support": true, "bulk_upload": true, "api_access": true}',
    '{"api_calls": 20000, "storage_gb": 100}',
    false, 3,
    'For established businesses requiring unlimited listings',
    '{"listings": "Unlimited listings", "photos_per_listing": "20 photos per listing", "featured_slots": "15 featured listing slots", "api_access": "Full API access"}'
),

-- Restaurant plans
(
    'Basic Restaurant', 'basic', 'restaurant', 19.99, 199.99, 7,
    '{"menu_items": 100, "photos_per_item": 3, "online_ordering": true, "delivery_integration": false}',
    '{"orders_per_month": 500, "storage_gb": 10}',
    true, 1,
    'Essential features for small restaurants',
    '{"menu_items": "Up to 100 menu items", "photos_per_item": "3 photos per menu item", "online_ordering": "Online ordering system"}'
),
(
    'Professional Restaurant', 'professional', 'restaurant', 49.99, 499.99, 7,
    '{"menu_items": 500, "photos_per_item": 5, "online_ordering": true, "delivery_integration": true, "analytics": true, "promotions": true}',
    '{"orders_per_month": 2000, "storage_gb": 50}',
    false, 2,
    'Advanced features for growing restaurants',
    '{"menu_items": "Up to 500 menu items", "delivery_integration": "Integrated delivery management", "analytics": "Sales and customer analytics"}'
);

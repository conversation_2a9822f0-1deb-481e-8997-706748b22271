package repositories

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/paradoxe35/torra/packages/database"
	"github.com/paradoxe35/torra/packages/errors"
	"github.com/paradoxe35/torra/packages/security"
	"github.com/paradoxe35/torra/services/core/internal/domain/entities"
)

func setupRefreshTokenRepo(t *testing.T) (*refreshTokenRepository, *database.DB, func()) {
	// Skip test if database testing is not enabled
	if !database.IsTestingEnabled() {
		t.Skip("Skipping test: database testing not enabled. Set ENABLE_DB_TESTS=true or run in CI")
	}

	// Setup test database
	testDB, err := database.SetupTestDB(nil) // Use default config
	if err != nil {
		t.Fatalf("Failed to setup test database: %v", err)
	}

	repo := &refreshTokenRepository{db: testDB.DB}

	cleanup := func() {
		if err := testDB.Close(); err != nil {
			t.Errorf("Failed to cleanup test database: %v", err)
		}
	}

	return repo, testDB.DB, cleanup
}

func createTestUserForRefreshToken(t *testing.T, db *database.DB) uuid.UUID {
	// First create a test area
	areaID := uuid.New()
	areaCode := fmt.Sprintf("TEST_%s", areaID.String()[:8])

	areaQuery := `
		INSERT INTO areas (
			id, type, name, code, center_lat, center_lng,
			center_point, operational_radius, status,
			service_availability, settings, switching_mode,
			allow_cross_trade, nearby_area_ids, transition_zones,
			timezone, languages, currency, tax_rate, min_order_value,
			delivery_fee_structure, cross_area_fees, operational_hours,
			created_at, updated_at
		) VALUES (
			$1, 'town', 'Test Area', $2, 0, 0,
			ST_SetSRID(ST_MakePoint(0, 0), 4326), 10, 'active',
			'{}', '{}', 'manual',
			false, '{}', '[]',
			'UTC', '{"en"}', 'USD', 0, 0,
			'{}', '{}', '{}',
			NOW(), NOW()
		)`

	_, err := db.Exec(areaQuery, areaID, areaCode)
	require.NoError(t, err)

	// Now create a test user
	userID := uuid.New()
	userQuery := `
		INSERT INTO users (
			id, email, phone, name, password_hash,
			profile_image_url, email_verified, phone_verified,
			id_verified, verification_level, google_id, facebook_id,
			mfa_enabled, mfa_secret, area_id, last_known_lat, last_known_lng,
			status, created_at, updated_at
		) VALUES (
			$1, $2, $3, 'Test User', 'hashed_password',
			'', false, false,
			false, 0, NULL, NULL,
			false, NULL, $4, NULL, NULL,
			'active', NOW(), NOW()
		)`

	email := fmt.Sprintf("<EMAIL>", userID.String()[:8])
	phone := fmt.Sprintf("+123456%s", userID.String()[:4])

	_, err = db.Exec(userQuery, userID, email, phone, areaID)
	require.NoError(t, err)

	return userID
}

func TestRefreshTokenRepository_Create(t *testing.T) {
	repo, db, cleanup := setupRefreshTokenRepo(t)
	defer cleanup()

	ctx := context.Background()

	// Create a test user first
	userID := createTestUserForRefreshToken(t, db)

	// Generate a secure token
	rawToken, err := security.GenerateRefreshToken()
	require.NoError(t, err)

	// Hash the token for storage
	tokenHash := security.HashRefreshToken(rawToken)

	token := &entities.RefreshToken{
		ID:         uuid.New(),
		UserID:     userID, // Use the created user ID
		TokenHash:  tokenHash,
		DeviceID:   "test-device-123",
		DeviceName: "Test Device",
		IPAddress:  "***********",
		UserAgent:  "TestAgent/1.0",
		ExpiresAt:  time.Now().Add(30 * 24 * time.Hour),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// Test: Create refresh token
	err = repo.Create(ctx, token)
	assert.NoError(t, err)
}

func TestRefreshTokenRepository_FindByTokenHash(t *testing.T) {
	repo, db, cleanup := setupRefreshTokenRepo(t)
	defer cleanup()

	ctx := context.Background()

	// Create a test user first
	userID := createTestUserForRefreshToken(t, db)

	// Create a test token first
	rawToken, err := security.GenerateRefreshToken()
	require.NoError(t, err)
	tokenHash := security.HashRefreshToken(rawToken)

	expectedToken := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID, // Use the created user ID
		TokenHash: tokenHash,
		DeviceID:  "test-device-123",
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create the token
	err = repo.Create(ctx, expectedToken)
	require.NoError(t, err)

	// Test: Find by token hash
	foundToken, err := repo.FindByTokenHash(ctx, tokenHash)
	assert.NoError(t, err)
	assert.NotNil(t, foundToken)
	assert.Equal(t, expectedToken.ID, foundToken.ID)
	assert.Equal(t, expectedToken.UserID, foundToken.UserID)

	// Test: Find non-existent token
	nonExistentHash := security.HashRefreshToken("non-existent-token")
	_, err = repo.FindByTokenHash(ctx, nonExistentHash)
	assert.Error(t, err)
	assert.Equal(t, errors.ErrNotFound, err)
}

func TestRefreshTokenRepository_RevokeByID(t *testing.T) {
	repo, db, cleanup := setupRefreshTokenRepo(t)
	defer cleanup()

	ctx := context.Background()

	// Create a test user first
	userID := createTestUserForRefreshToken(t, db)

	// Create a test token
	token := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID, // Use the created user ID
		TokenHash: security.HashRefreshToken("test-token"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := repo.Create(ctx, token)
	require.NoError(t, err)

	// Test: Revoke token
	err = repo.RevokeByID(ctx, token.ID)
	assert.NoError(t, err)

	// Test: Verify token is revoked
	revokedToken, err := repo.FindByID(ctx, token.ID)
	assert.NoError(t, err)
	assert.NotNil(t, revokedToken.RevokedAt)

	// Test: Revoke non-existent token
	err = repo.RevokeByID(ctx, uuid.New())
	assert.Error(t, err)
	assert.Equal(t, errors.ErrNotFound, err)
}

func TestRefreshTokenRepository_FindActiveByUserID(t *testing.T) {
	repo, db, cleanup := setupRefreshTokenRepo(t)
	defer cleanup()

	ctx := context.Background()
	userID := createTestUserForRefreshToken(t, db)

	// Create multiple tokens for the user
	activeToken1 := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		TokenHash: security.HashRefreshToken("active-token-1"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	activeToken2 := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		TokenHash: security.HashRefreshToken("active-token-2"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(15 * 24 * time.Hour),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create an expired token
	expiredToken := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		TokenHash: security.HashRefreshToken("expired-token"),
		IPAddress: "***********",                  // Add valid IP address
		ExpiresAt: time.Now().Add(-1 * time.Hour), // Already expired
		CreatedAt: time.Now().Add(-30 * 24 * time.Hour),
		UpdatedAt: time.Now(),
	}

	// Create a revoked token
	revokedTime := time.Now()
	revokedToken := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		TokenHash: security.HashRefreshToken("revoked-token"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		RevokedAt: &revokedTime,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create all tokens
	require.NoError(t, repo.Create(ctx, activeToken1))
	require.NoError(t, repo.Create(ctx, activeToken2))
	require.NoError(t, repo.Create(ctx, expiredToken))
	require.NoError(t, repo.Create(ctx, revokedToken))

	// Test: Find active tokens only
	activeTokens, err := repo.FindActiveByUserID(ctx, userID)
	assert.NoError(t, err)
	assert.Len(t, activeTokens, 2) // Only 2 active tokens

	// Verify they are the active tokens
	for _, token := range activeTokens {
		assert.Nil(t, token.RevokedAt)
		assert.True(t, token.ExpiresAt.After(time.Now()))
	}
}

func TestRefreshTokenRepository_RevokeAllForUser(t *testing.T) {
	repo, db, cleanup := setupRefreshTokenRepo(t)
	defer cleanup()

	ctx := context.Background()
	userID := createTestUserForRefreshToken(t, db)

	// Create multiple tokens for the user
	token1 := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		TokenHash: security.HashRefreshToken("token-1"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	token2 := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		TokenHash: security.HashRefreshToken("token-2"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(15 * 24 * time.Hour),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create tokens
	require.NoError(t, repo.Create(ctx, token1))
	require.NoError(t, repo.Create(ctx, token2))

	// Test: Revoke all tokens for user
	err := repo.RevokeAllForUser(ctx, userID)
	assert.NoError(t, err)

	// Test: Verify all tokens are revoked
	activeTokens, err := repo.FindActiveByUserID(ctx, userID)
	assert.NoError(t, err)
	assert.Len(t, activeTokens, 0)
}

func TestRefreshTokenRepository_DeleteExpired(t *testing.T) {
	repo, db, cleanup := setupRefreshTokenRepo(t)
	defer cleanup()

	ctx := context.Background()

	// Create test users for the tokens
	userID1 := createTestUserForRefreshToken(t, db)
	userID2 := createTestUserForRefreshToken(t, db)
	userID3 := createTestUserForRefreshToken(t, db)

	// Create expired tokens
	expiredToken1 := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID1, // Use created user ID
		TokenHash: security.HashRefreshToken("expired-1"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(-1 * time.Hour),
		CreatedAt: time.Now().Add(-31 * 24 * time.Hour),
		UpdatedAt: time.Now(),
	}

	expiredToken2 := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID2, // Use created user ID
		TokenHash: security.HashRefreshToken("expired-2"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(-24 * time.Hour),
		CreatedAt: time.Now().Add(-60 * 24 * time.Hour),
		UpdatedAt: time.Now(),
	}

	// Create active token
	activeToken := &entities.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID3, // Use created user ID
		TokenHash: security.HashRefreshToken("active"),
		IPAddress: "***********", // Add valid IP address
		ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create all tokens
	require.NoError(t, repo.Create(ctx, expiredToken1))
	require.NoError(t, repo.Create(ctx, expiredToken2))
	require.NoError(t, repo.Create(ctx, activeToken))

	// Test: Delete expired tokens
	deletedCount, err := repo.DeleteExpired(ctx)
	assert.NoError(t, err)
	assert.Equal(t, int64(2), deletedCount)

	// Test: Verify active token still exists
	foundToken, err := repo.FindByID(ctx, activeToken.ID)
	assert.NoError(t, err)
	assert.NotNil(t, foundToken)
}

func TestRefreshTokenEntity_IsValid(t *testing.T) {
	// Test valid token
	validToken := &entities.RefreshToken{
		ExpiresAt: time.Now().Add(1 * time.Hour),
		RevokedAt: nil,
	}
	assert.True(t, validToken.IsValid())

	// Test expired token
	expiredToken := &entities.RefreshToken{
		ExpiresAt: time.Now().Add(-1 * time.Hour),
		RevokedAt: nil,
	}
	assert.False(t, expiredToken.IsValid())

	// Test revoked token
	revokedTime := time.Now()
	revokedToken := &entities.RefreshToken{
		ExpiresAt: time.Now().Add(1 * time.Hour),
		RevokedAt: &revokedTime,
	}
	assert.False(t, revokedToken.IsValid())
}

func TestRefreshTokenEntity_GetStatus(t *testing.T) {
	// Test active status
	activeToken := &entities.RefreshToken{
		ExpiresAt: time.Now().Add(1 * time.Hour),
		RevokedAt: nil,
	}
	assert.Equal(t, entities.RefreshTokenStatusActive, activeToken.GetStatus())

	// Test expired status
	expiredToken := &entities.RefreshToken{
		ExpiresAt: time.Now().Add(-1 * time.Hour),
		RevokedAt: nil,
	}
	assert.Equal(t, entities.RefreshTokenStatusExpired, expiredToken.GetStatus())

	// Test revoked status
	revokedTime := time.Now()
	revokedToken := &entities.RefreshToken{
		ExpiresAt: time.Now().Add(1 * time.Hour),
		RevokedAt: &revokedTime,
	}
	assert.Equal(t, entities.RefreshTokenStatusRevoked, revokedToken.GetStatus())
}

# 1.2 Subscription Engine Implementation Plan

## Overview
This document outlines the detailed implementation plan for the generic subscription management system within the core service. This follows the clean architecture patterns established in SERVICE_GUIDELINES.md and will be integrated into the existing core service structure.

---

## 1. Architecture Overview

### 1.1 Clean Architecture Structure
```
services/core/
├── internal/
│   ├── domain/
│   │   ├── entities/
│   │   │   ├── subscription.go
│   │   │   ├── subscription_tier.go
│   │   │   ├── billing_cycle.go
│   │   │   └── payment_transaction.go
│   │   └── valueobjects/
│   │       ├── subscription_status.go
│   │       ├── billing_period.go
│   │       └── payment_method.go
│   ├── application/
│   │   ├── services/
│   │   │   ├── subscription_service.go
│   │   │   ├── billing_service.go
│   │   │   └── payment_gateway_service.go
│   │   ├── ports/
│   │   │   ├── subscription_repository.go
│   │   │   ├── subscription_tier_repository.go
│   │   │   ├── payment_repository.go
│   │   │   └── payment_gateway.go
│   │   └── dtos/
│   │       ├── subscription_dto.go
│   │       ├── create_subscription_dto.go
│   │       ├── upgrade_subscription_dto.go
│   │       └── payment_dto.go
│   └── adapters/
│       ├── http/
│       │   └── handlers/
│       │       ├── subscription_handler.go
│       │       └── billing_handler.go
│       ├── postgres/
│       │   ├── models/
│       │   │   ├── subscription_model.go
│       │   │   ├── subscription_tier_model.go
│       │   │   └── payment_transaction_model.go
│       │   └── repositories/
│       │       ├── subscription_repository.go
│       │       ├── subscription_tier_repository.go
│       │       └── payment_repository.go
│       └── payment/
│           ├── stripe_gateway.go
│           └── mock_gateway.go
├── migrations/
│   ├── 000X_create_subscription_tiers.up.sql
│   ├── 000X_create_subscription_tiers.down.sql
│   ├── 000Y_create_subscriptions.up.sql
│   ├── 000Y_create_subscriptions.down.sql
│   ├── 000Z_create_payment_transactions.up.sql
│   └── 000Z_create_payment_transactions.down.sql
└── tests/
    ├── unit/
    ├── integration/
    └── fixtures/
```

---

## 2. Domain Layer Implementation

### 2.1 Domain Entities

#### Task 1: Create Subscription Entity
**File:** `internal/domain/entities/subscription.go`
- [ ] Define Subscription struct with fields:
  - ID (uuid.UUID)
  - UserID (uuid.UUID)
  - TierID (uuid.UUID)
  - Status (SubscriptionStatus)
  - BillingCycle (BillingCycle)
  - CurrentPeriodStart (time.Time)
  - CurrentPeriodEnd (time.Time)
  - TrialEndsAt (*time.Time)
  - PausedAt (*time.Time)
  - CanceledAt (*time.Time)
  - CreatedAt (time.Time)
  - UpdatedAt (time.Time)
- [ ] Implement business methods:
  - `IsActive() bool`
  - `IsInTrial() bool`
  - `CanUpgrade() bool`
  - `CanDowngrade() bool`
  - `Pause() error`
  - `Resume() error`
  - `Cancel() error`
- [ ] Write unit tests in `subscription_test.go`

#### Task 2: Create Subscription Tier Entity
**File:** `internal/domain/entities/subscription_tier.go`
- [ ] Define SubscriptionTier struct with fields:
  - ID (uuid.UUID)
  - Name (string)
  - Code (string) // "basic", "professional", "premium"
  - Price (decimal.Decimal)
  - Currency (string)
  - Features (map[string]interface{})
  - Limits (map[string]int)
  - TrialDays (int)
  - SortOrder (int)
  - IsActive (bool)
- [ ] Implement validation methods:
  - `Validate() error`
  - `HasFeature(feature string) bool`
  - `GetLimit(resource string) int`
- [ ] Write unit tests

#### Task 3: Create Billing Cycle Entity
**File:** `internal/domain/entities/billing_cycle.go`
- [ ] Define BillingCycle struct with fields:
  - ID (uuid.UUID)
  - SubscriptionID (uuid.UUID)
  - StartDate (time.Time)
  - EndDate (time.Time)
  - Amount (decimal.Decimal)
  - Status (string)
  - InvoiceID (*string)
  - PaidAt (*time.Time)
- [ ] Implement methods:
  - `IsCurrent() bool`
  - `IsOverdue() bool`
  - `MarkAsPaid(transactionID string) error`
- [ ] Write unit tests

#### Task 4: Create Payment Transaction Entity
**File:** `internal/domain/entities/payment_transaction.go`
- [ ] Define PaymentTransaction struct with fields:
  - ID (uuid.UUID)
  - SubscriptionID (uuid.UUID)
  - Amount (decimal.Decimal)
  - Currency (string)
  - Type (string) // "subscription", "upgrade", "downgrade"
  - Status (string)
  - GatewayTransactionID (string)
  - GatewayResponse (json.RawMessage)
  - ProcessedAt (*time.Time)
- [ ] Write unit tests

### 2.2 Value Objects

#### Task 5: Create Value Objects
**File:** `internal/domain/valueobjects/subscription_status.go`
- [ ] Define SubscriptionStatus enum with values:
  - Active
  - Trialing
  - PastDue
  - Canceled
  - Paused
  - Expired
- [ ] Implement validation and string methods
- [ ] Write unit tests

**File:** `internal/domain/valueobjects/billing_period.go`
- [ ] Define BillingPeriod enum:
  - Monthly
  - Annual
  - Custom
- [ ] Implement calculation methods:
  - `GetNextBillingDate(from time.Time) time.Time`
  - `GetPeriodDays() int`
- [ ] Write unit tests

---

## 3. Application Layer Implementation

### 3.1 Port Definitions

#### Task 6: Define Repository Interfaces
**File:** `internal/application/ports/subscription_repository.go`
```go
type SubscriptionRepository interface {
    Create(ctx context.Context, subscription *entities.Subscription) error
    Update(ctx context.Context, subscription *entities.Subscription) error
    FindByID(ctx context.Context, id uuid.UUID) (*entities.Subscription, error)
    FindByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.Subscription, error)
    FindActiveByUserID(ctx context.Context, userID uuid.UUID) (*entities.Subscription, error)
    FindExpiring(ctx context.Context, beforeDate time.Time) ([]*entities.Subscription, error)
    CountByTier(ctx context.Context, tierID uuid.UUID) (int64, error)
}
```
- [ ] Define all repository interfaces
- [ ] Write interface documentation

#### Task 7: Define Payment Gateway Interface
**File:** `internal/application/ports/payment_gateway.go`
```go
type PaymentGateway interface {
    CreateCustomer(ctx context.Context, user *entities.User) (string, error)
    CreateSubscription(ctx context.Context, req CreateSubscriptionRequest) (*PaymentResponse, error)
    UpdateSubscription(ctx context.Context, req UpdateSubscriptionRequest) (*PaymentResponse, error)
    CancelSubscription(ctx context.Context, subscriptionID string) error
    ProcessPayment(ctx context.Context, req PaymentRequest) (*PaymentResponse, error)
    GetPaymentMethods(ctx context.Context, customerID string) ([]PaymentMethod, error)
}
```
- [ ] Define payment gateway abstraction
- [ ] Define request/response DTOs

### 3.2 Application Services

#### Task 8: Implement Subscription Service
**File:** `internal/application/services/subscription_service.go`
- [ ] Implement core subscription logic:
  ```go
  type SubscriptionService struct {
      subscriptionRepo ports.SubscriptionRepository
      tierRepo        ports.SubscriptionTierRepository
      paymentGateway  ports.PaymentGateway
      eventBus        ports.EventBus
      logger          logger.Interface
      translator      *i18n.Translator
  }
  ```
- [ ] Implement methods:
  - `CreateSubscription(ctx, userID, tierID, paymentMethod)`
  - `UpgradeSubscription(ctx, subscriptionID, newTierID)`
  - `DowngradeSubscription(ctx, subscriptionID, newTierID)`
  - `PauseSubscription(ctx, subscriptionID)`
  - `ResumeSubscription(ctx, subscriptionID)`
  - `CancelSubscription(ctx, subscriptionID)`
  - `ProcessTrialExpiration(ctx)`
  - `EnforceUsageLimits(ctx, subscriptionID, resource, usage)`
- [ ] Write comprehensive unit tests with mocks
- [ ] Test all edge cases and error scenarios

#### Task 9: Implement Billing Service
**File:** `internal/application/services/billing_service.go`
- [ ] Implement billing cycle management:
  ```go
  type BillingService struct {
      subscriptionRepo ports.SubscriptionRepository
      paymentRepo     ports.PaymentRepository
      paymentGateway  ports.PaymentGateway
      notificationSvc ports.NotificationService
      logger          logger.Interface
  }
  ```
- [ ] Implement methods:
  - `ProcessRecurringBilling(ctx)`
  - `HandlePaymentWebhook(ctx, payload)`
  - `RetryFailedPayments(ctx)`
  - `GenerateInvoice(ctx, subscriptionID)`
  - `RefundPayment(ctx, transactionID, amount)`
- [ ] Write unit tests with mocked dependencies

### 3.3 DTOs

#### Task 10: Create DTOs
**File:** `internal/application/dtos/subscription_dto.go`
- [ ] Define all DTOs:
  ```go
  type CreateSubscriptionDTO struct {
      UserID         uuid.UUID `json:"user_id" validate:"required"`
      TierID         uuid.UUID `json:"tier_id" validate:"required"`
      PaymentMethod  string    `json:"payment_method" validate:"required"`
      StartTrial     bool      `json:"start_trial"`
  }
  
  type UpgradeSubscriptionDTO struct {
      NewTierID      uuid.UUID `json:"new_tier_id" validate:"required"`
      Immediate      bool      `json:"immediate"`
  }
  
  type SubscriptionResponseDTO struct {
      ID             uuid.UUID              `json:"id"`
      User           UserSummaryDTO         `json:"user"`
      Tier           TierDTO                `json:"tier"`
      Status         string                 `json:"status"`
      CurrentPeriod  PeriodDTO              `json:"current_period"`
      NextBilling    *time.Time             `json:"next_billing,omitempty"`
      TrialEndsAt    *time.Time             `json:"trial_ends_at,omitempty"`
  }
  ```
- [ ] Add validation tags
- [ ] Write unit tests for validation

---

## 4. Adapters Layer Implementation

### 4.1 HTTP Handlers

#### Task 11: Implement Subscription Handler
**File:** `internal/adapters/http/handlers/subscription_handler.go`
- [ ] Implement REST endpoints:
  ```go
  type SubscriptionHandler struct {
      subscriptionService *services.SubscriptionService
      logger              logger.Interface
      translator          *i18n.Translator
  }
  ```
- [ ] Implement handlers:
  - `POST /api/v1/subscriptions` - Create subscription
  - `GET /api/v1/subscriptions/current` - Get current user subscription
  - `GET /api/v1/subscriptions/:id` - Get subscription details
  - `PUT /api/v1/subscriptions/:id/upgrade` - Upgrade subscription
  - `PUT /api/v1/subscriptions/:id/downgrade` - Downgrade subscription
  - `PUT /api/v1/subscriptions/:id/pause` - Pause subscription
  - `PUT /api/v1/subscriptions/:id/resume` - Resume subscription
  - `DELETE /api/v1/subscriptions/:id` - Cancel subscription
  - `GET /api/v1/subscription-tiers` - List available tiers
- [ ] Add proper error handling with i18n
- [ ] Write handler tests with httptest

#### Task 12: Implement Billing Handler
**File:** `internal/adapters/http/handlers/billing_handler.go`
- [ ] Implement billing endpoints:
  - `GET /api/v1/billing/history` - Payment history
  - `GET /api/v1/billing/invoices/:id` - Download invoice
  - `POST /api/v1/billing/payment-methods` - Add payment method
  - `DELETE /api/v1/billing/payment-methods/:id` - Remove payment method
  - `POST /webhooks/payment` - Payment gateway webhook
- [ ] Write handler tests

### 4.2 PostgreSQL Repositories

#### Task 13: Create Database Models
**File:** `internal/adapters/postgres/models/subscription_model.go`
- [ ] Define database models with proper tags:
  ```go
  type SubscriptionModel struct {
      ID                 uuid.UUID      `db:"id"`
      UserID             uuid.UUID      `db:"user_id"`
      TierID             uuid.UUID      `db:"tier_id"`
      Status             string         `db:"status"`
      BillingCycle       string         `db:"billing_cycle"`
      CurrentPeriodStart time.Time      `db:"current_period_start"`
      CurrentPeriodEnd   time.Time      `db:"current_period_end"`
      TrialEndsAt        *time.Time     `db:"trial_ends_at"`
      PausedAt           *time.Time     `db:"paused_at"`
      CanceledAt         *time.Time     `db:"canceled_at"`
      CreatedAt          time.Time      `db:"created_at"`
      UpdatedAt          time.Time      `db:"updated_at"`
  }
  ```
- [ ] Create mapping functions to/from domain entities

#### Task 14: Implement Subscription Repository
**File:** `internal/adapters/postgres/repositories/subscription_repository.go`
- [ ] Implement repository with sqlx:
  ```go
  type subscriptionRepository struct {
      db *database.DB
  }
  ```
- [ ] Implement all interface methods with proper SQL queries
- [ ] Add transaction support for complex operations
- [ ] Write integration tests using testcontainers
- [ ] Test all CRUD operations
- [ ] Test complex queries and edge cases

#### Task 15: Implement Tier Repository
**File:** `internal/adapters/postgres/repositories/subscription_tier_repository.go`
- [ ] Implement tier management repository
- [ ] Add caching for tier lookups
- [ ] Write integration tests

### 4.3 Payment Gateway Adapters

#### Task 16: Implement Payment Gateway Adapters
**File:** `internal/adapters/payment/stripe_gateway.go`
- [ ] Implement Stripe payment gateway:
  ```go
  type StripeGateway struct {
      client *stripe.Client
      logger logger.Interface
  }
  ```
- [ ] Implement all PaymentGateway interface methods
- [ ] Handle Stripe webhooks
- [ ] Write unit tests with mocked Stripe client

**File:** `internal/adapters/payment/mock_gateway.go`
- [ ] Create mock gateway for testing
- [ ] Simulate various payment scenarios

---

## 5. Database Migrations

### Task 17: Create Migration Files

#### Migration 1: Subscription Tiers Table
**File:** `migrations/00XX_create_subscription_tiers.up.sql`
```sql
CREATE TABLE subscription_tiers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    features JSONB NOT NULL DEFAULT '{}',
    limits JSONB NOT NULL DEFAULT '{}',
    trial_days INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_subscription_tiers_code ON subscription_tiers(code);
CREATE INDEX idx_subscription_tiers_active ON subscription_tiers(is_active);
```

#### Migration 2: Subscriptions Table
**File:** `migrations/00XY_create_subscriptions.up.sql`
```sql
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    tier_id UUID NOT NULL REFERENCES subscription_tiers(id),
    status VARCHAR(50) NOT NULL,
    billing_cycle VARCHAR(20) NOT NULL,
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    trial_ends_at TIMESTAMP,
    paused_at TIMESTAMP,
    canceled_at TIMESTAMP,
    stripe_subscription_id VARCHAR(255),
    stripe_customer_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    CONSTRAINT unique_active_subscription UNIQUE (user_id, status) 
        WHERE status IN ('active', 'trialing')
);

CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_trial_ends ON subscriptions(trial_ends_at) 
    WHERE trial_ends_at IS NOT NULL;
```

#### Migration 3: Payment Transactions Table
**File:** `migrations/00XZ_create_payment_transactions.up.sql`
```sql
CREATE TABLE payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id UUID NOT NULL REFERENCES subscriptions(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    gateway_transaction_id VARCHAR(255),
    gateway_response JSONB,
    processed_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_payment_transactions_subscription ON payment_transactions(subscription_id);
CREATE INDEX idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX idx_payment_transactions_gateway_id ON payment_transactions(gateway_transaction_id);
```

---

## 6. Testing Strategy

### 6.1 Unit Tests

#### Task 18: Domain Layer Tests
- [ ] Test all entity business logic
- [ ] Test value object validations
- [ ] Test edge cases and error conditions
- [ ] Achieve >90% coverage

#### Task 19: Service Layer Tests
- [ ] Mock all repository interfaces
- [ ] Mock payment gateway
- [ ] Test all service methods
- [ ] Test error handling
- [ ] Test transaction rollback scenarios

### 6.2 Integration Tests

#### Task 20: Repository Integration Tests
- [ ] Use testcontainers for PostgreSQL
- [ ] Test all CRUD operations
- [ ] Test complex queries
- [ ] Test transactions
- [ ] Test concurrent operations

#### Task 21: Handler Integration Tests
- [ ] Test all HTTP endpoints
- [ ] Test authentication/authorization
- [ ] Test validation
- [ ] Test error responses
- [ ] Test i18n responses

### 6.3 End-to-End Tests

#### Task 22: Complete Flow Tests
- [ ] Test subscription creation flow
- [ ] Test upgrade/downgrade flow
- [ ] Test payment processing flow
- [ ] Test webhook handling
- [ ] Test trial expiration flow
- [ ] Test recurring billing flow

---

## 7. Configuration & Environment

### Task 23: Update Configuration
**File:** `services/core/configs/config.yaml`
```yaml
subscription:
  trial_days_default: 7
  grace_period_days: 3
  webhook_secret: ${WEBHOOK_SECRET}
  
payment:
  gateway: stripe
  stripe:
    secret_key: ${STRIPE_SECRET_KEY}
    publishable_key: ${STRIPE_PUBLISHABLE_KEY}
    webhook_endpoint_secret: ${STRIPE_WEBHOOK_SECRET}
  
billing:
  process_hour: 2  # 2 AM
  retry_attempts: 3
  retry_delay_hours: 24
```

---

## 8. Monitoring & Observability

### Task 24: Add Metrics and Logging
- [ ] Add structured logging to all services
- [ ] Add metrics for:
  - Subscription creation rate
  - Upgrade/downgrade rate
  - Churn rate
  - Payment success rate
  - Average subscription value
- [ ] Add alerts for:
  - Payment failures
  - High churn rate
  - Gateway errors

---

## 9. Background Jobs

### Task 25: Implement Background Workers
**Using Asynq as defined in PLAN.md:**

- [ ] Trial expiration checker (daily)
- [ ] Recurring billing processor (daily)
- [ ] Failed payment retry (every 6 hours)
- [ ] Subscription metrics calculator (hourly)
- [ ] Usage limit enforcer (real-time)

---

## 10. API Documentation

### Task 26: Create OpenAPI Specification
**File:** `docs/api/subscription.v1.yaml`
- [ ] Document all endpoints
- [ ] Define request/response schemas
- [ ] Add authentication requirements
- [ ] Add example requests/responses

---

## 11. Implementation Timeline

### Week 1: Domain & Application Layers
- Days 1-2: Domain entities and value objects
- Days 3-4: Application services and ports
- Day 5: DTOs and validation

### Week 2: Adapters & Infrastructure
- Days 1-2: PostgreSQL repositories
- Days 3-4: HTTP handlers
- Day 5: Payment gateway integration

### Week 3: Testing & Polish
- Days 1-2: Unit tests
- Days 3-4: Integration tests
- Day 5: Documentation and cleanup

### Week 4: Production Readiness
- Days 1-2: Background jobs
- Days 3-4: Monitoring and metrics
- Day 5: Load testing and optimization

---

## 12. Dependencies & Imports

### Required Packages
```go
// Domain layer - no external dependencies

// Application layer
"github.com/google/uuid"
"github.com/shopspring/decimal"

// Infrastructure layer
"github.com/jmoiron/sqlx"
"github.com/stripe/stripe-go/v74"
"github.com/hibiken/asynq"

// Shared packages (already in monorepo)
"github.com/paradoxe35/torra/packages/config"
"github.com/paradoxe35/torra/packages/database"
"github.com/paradoxe35/torra/packages/logger"
"github.com/paradoxe35/torra/packages/errors"
"github.com/paradoxe35/torra/packages/http"
"github.com/paradoxe35/torra/packages/i18n"
"github.com/paradoxe35/torra/packages/cache"
```

---

## 13. Success Criteria

### Definition of Done
- [ ] All tests passing with >80% coverage
- [ ] No golangci-lint errors
- [ ] API documentation complete
- [ ] Integration with core service auth
- [ ] i18n support for all user-facing messages
- [ ] Metrics and logging implemented
- [ ] Load tested for 1000 concurrent users
- [ ] Security review completed
- [ ] Database migrations tested
- [ ] Rollback procedures documented

---

## 14. Risk Mitigation

### Technical Risks
1. **Payment Gateway Downtime**
   - Mitigation: Implement retry logic and queue failed payments
   - Fallback: Support multiple payment gateways

2. **Database Performance**
   - Mitigation: Add proper indexes and use read replicas
   - Monitor: Query performance and connection pools

3. **Concurrent Subscription Updates**
   - Mitigation: Use database locks and transactions
   - Test: Race conditions in integration tests

### Business Risks
1. **Subscription Abuse**
   - Mitigation: Rate limiting and fraud detection
   - Monitor: Unusual subscription patterns

2. **Revenue Leakage**
   - Mitigation: Audit trail for all subscription changes
   - Monitor: Failed payment rates and retry success

---

## 15. Future Enhancements

### Phase 2 Considerations
- [ ] Multiple payment methods per subscription
- [ ] Family/team subscriptions
- [ ] Usage-based billing tiers
- [ ] Promotional codes and discounts
- [ ] Subscription analytics dashboard
- [ ] A/B testing for pricing
- [ ] Dunning management
- [ ] Revenue recognition compliance

---

## Notes

1. **Critical Path Items:**
   - Payment gateway integration must be completed before testing billing flows
   - Database migrations must be reviewed before deployment
   - Security audit required for payment handling code

2. **Dependencies on Other Services:**
   - User service for customer information
   - Notification service for email/SMS
   - Area service for location-based pricing (future)

3. **Performance Targets:**
   - Subscription creation: < 500ms
   - Payment processing: < 2s
   - Tier lookup: < 50ms (cached)
   - Billing job processing: 1000 subscriptions/minute

4. **Compliance Requirements:**
   - PCI DSS compliance for payment data
   - GDPR compliance for user data
   - Revenue recognition standards
   - Tax calculation requirements

---

This plan provides a complete roadmap for implementing a production-ready subscription engine in the core service, following all established patterns and best practices from the MyTorra platform architecture.